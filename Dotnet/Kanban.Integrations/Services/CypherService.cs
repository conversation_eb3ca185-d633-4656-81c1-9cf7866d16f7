using System.Text;
using Kanban.Integrations.Ado;

namespace Kanban.Integrations.Services;

public static class CypherService
{
  public static string TransformToCypher(string boardUrl, string boardTitle, WorkItemModel[] workItems)
  {
    var uctNow = DateTime.UtcNow.ToString("O");
    var defaultUser = "11111111-1111-1111-1111-111111111111";
    var cypher = new StringBuilder();
    var columnIds = workItems.Select(w => w.Fields.State)
      .Distinct()
      .ToDictionary(state => state, _ => Guid.NewGuid());
    foreach (var workItem in workItems)
    {
      cypher.AppendLine($"MERGE (i:Import {{ url: '{boardUrl}' }})");
      cypher.AppendLine("ON CREATE SET i.externalId = randomUUID()");
      cypher.AppendLine($"MERGE (i)<-[:IMPORTED_FROM]-(b:Board {{ name: '{boardTitle}' }})");
      cypher.AppendLine($"ON CREATE SET b.externalId = randomUUID(), b.createdOn = '{uctNow}', b.createdBy = '{defaultUser}', b.columnOrder = [{string.Join(", ", columnIds.Values.Select(v=> $"'{v}'"))}]");
      cypher.AppendLine($"MERGE (b)<-[:IS_STATE_IN]-(col:Column {{ name: '{workItem.Fields.State}' }})");
      cypher.AppendLine($"ON CREATE SET col.externalId = '{columnIds[workItem.Fields.State]}', col.createdOn = '{uctNow}', col.modifiedOn = '{uctNow}', col.cardPriorityOrder = [] ");
      cypher.AppendLine($"MERGE (col)<-[:HAS_STATE]-(c:Card {{ importedId: '{workItem.Id}' }} )");
      cypher.AppendLine("ON CREATE SET");
      cypher.AppendLine("  c.externalId = randomUUID(),");
      OutputSetClause(cypher, workItem);
      cypher.AppendLine("ON MATCH SET");
      OutputSetClause(cypher, workItem);
      cypher.AppendLine(";");
    }

    return cypher.ToString();
  }

  private static void OutputSetClause(StringBuilder cypher, WorkItemModel workItem)
  {
    cypher.AppendLine($"  c.title= '{workItem.Fields.Title}',");
    cypher.AppendLine($"  c.description= '{workItem.Fields.Description}',");
    cypher.AppendLine($"  c.createdBy= '{workItem.Fields.CreatedBy.UserName}',");
    cypher.AppendLine($"  c.createdOn= '{workItem.Fields.CreatedDate.ToString("O")}',");
    cypher.AppendLine($"  c.modifiedBy= '{workItem.Fields.ChangedBy.UserName}',");
    cypher.AppendLine($"  c.modifiedOn= '{workItem.Fields.ChangedDate}',");
    cypher.AppendLine($"  c.tags= []");
    
    // cypher.AppendLine($"Id: {workItem.Id},");
    // cypher.AppendLine($"Rev: {workItem.Rev},");
    // cypher.AppendLine($"Project: '{workItem.Fields.Project}',");
    // cypher.AppendLine($"WorkItemType: '{workItem.Fields.WorkItemType}',");
    // cypher.AppendLine($"Reason: '{workItem.Fields.Reason}',");
    // cypher.AppendLine($"Column: '{workItem.Fields.Column}'");

  }
}
