using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Kanban.Integrations.Ado;

namespace Kanban.Integrations.Services;

public static class AzureDevops
{
  public static async Task<WorkItemModel[]> ExportWorkItems(string organizationUrl, string projectName,
    string personalAccessToken)
  {
    using HttpClient client = new HttpClient();

    var authToken = Convert.ToBase64String(Encoding.ASCII.GetBytes($":{personalAccessToken}"));
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);

// Project details
    string projectUrl = $"{organizationUrl}/_apis/projects/{projectName}?api-version=7.0";
    Console.WriteLine("Retrieving project details...");
    await GetAndDisplayDataAsync(client, projectUrl);

//  Work Items
    Console.WriteLine("\nRetrieving all work items...");
    var result = await GetWorkItemsAsync(client, organizationUrl, projectName);

    Console.WriteLine("\nData extraction complete.");

    return result;
  }

  static async Task GetAndDisplayDataAsync(HttpClient client, string url)
  {
    try
    {
      HttpResponseMessage response = await client.GetAsync(url);

      if (response.IsSuccessStatusCode)
      {
        // Read and display the response content (JSON)
        string content = await response.Content.ReadAsStringAsync();
        Console.WriteLine("Response from " + url + ":");
        Console.WriteLine(content);
      }
      else
      {
        Console.WriteLine($"Error retrieving data from {url}. Status Code: {response.StatusCode}");
        string errorContent = await response.Content.ReadAsStringAsync();
        Console.WriteLine("Error details: " + errorContent);
      }
    }
    catch (Exception ex)
    {
      Console.WriteLine($"Exception when calling {url}: {ex.Message}");
    }
  }

  static async Task<WorkItemModel[]> GetWorkItemsAsync(HttpClient client, string organizationUrl, string projectName)
  {
    //WIQL
    var query = new
    {
      query =
        $"SELECT [System.Id], [System.AssignedTo], [System.State], [System.Title], [System.Tags] FROM WorkItems WHERE [System.TeamProject] = '{projectName}' ORDER BY [System.ChangedDate] DESC"
    };

    string queryJson = JsonSerializer.Serialize(query);
    // WIQL endpoint for the project
    string wiqlUrl = $"{organizationUrl}/{projectName}/_apis/wit/wiql?api-version=7.0";
    using var content = new StringContent(queryJson, Encoding.UTF8, "application/json");

    Console.WriteLine("Executing WIQL query to retrieve work item IDs...");
    HttpResponseMessage wiqlResponse = await client.PostAsync(wiqlUrl, content);

    if (!wiqlResponse.IsSuccessStatusCode)
    {
      Console.WriteLine($"Failed to run WIQL query. Status code: {wiqlResponse.StatusCode}");
      string error = await wiqlResponse.Content.ReadAsStringAsync();
      Console.WriteLine("Error: " + error);
      return [];
    }

    string wiqlResult = await wiqlResponse.Content.ReadAsStringAsync();

    var workItemIds = new List<int>();

    try
    {
      using JsonDocument doc = JsonDocument.Parse(wiqlResult);
      JsonElement root = doc.RootElement;
      if (root.TryGetProperty("workItems", out JsonElement workItemsElement))
      {
        foreach (JsonElement item in workItemsElement.EnumerateArray())
        {
          if (item.TryGetProperty("id", out JsonElement idElement) && idElement.TryGetInt32(out int id))
          {
            workItemIds.Add(id);
          }
        }
      }
    }
    catch (Exception ex)
    {
      Console.WriteLine("Error parsing WIQL response: " + ex.Message);
      return [];
    }

    if (workItemIds.Count == 0)
    {
      Console.WriteLine("No work items found for the project.");
      return [];
    }

    Console.WriteLine($"Found {workItemIds.Count} work items. Retrieving details...");

    // Azure DevOps REST API may limit the number of work items returned in a single call.
    // For simplicity, this example assumes all work items can be retrieved in one call.
    // For larger datasets, consider batching the requests.
    string idsString = string.Join(",", workItemIds);
    string workItemsUrl = $"{organizationUrl}/{projectName}/_apis/wit/workitems?ids={idsString}&api-version=7.0";

    HttpResponseMessage workItemsResponse = await client.GetAsync(workItemsUrl);

    if (!workItemsResponse.IsSuccessStatusCode)
    {
      Console.WriteLine($"Failed to retrieve work item details. Status code: {workItemsResponse.StatusCode}");
      string error = await workItemsResponse.Content.ReadAsStringAsync();
      Console.WriteLine("Error: " + error);
      return [];
    }

    string workItemsResult = await workItemsResponse.Content.ReadAsStringAsync();

    var options = new JsonSerializerOptions
    {
      PropertyNameCaseInsensitive = true,
      AllowTrailingCommas = true,
      ReadCommentHandling = JsonCommentHandling.Skip,
    };
    options.Converters.Add(new TextSanitizerConverter());
    var workItems = JsonSerializer.Deserialize<WorkItemRoot>(workItemsResult, options);
    return workItems.Value;
  }

  public static async Task<object> ExportUsers(string organizationUrl, string personalAccessToken)
  {
    throw new NotImplementedException();
  }
}
